// src/pages/Reports/ApprovedSiteVisitsReport.tsx
import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, MapPin, User, Building2, CheckCircle2, Clock, XCircle, Users, Car, MessageSquare, Briefcase } from "lucide-react";
import { useGetSpecialBookingsQuery, useGetVehiclesQuery } from "@/redux/slices/logistics";
import { apiSlice } from "@/redux/apiSlice";
import { noAuthHeader } from "@/utils/header";

// Create API endpoint for approved site visits
const approvedSiteVisitsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getApprovedSiteVisits: builder.query({
      query: (params) => ({
        url: '/approved-site-visits/',
        method: 'GET',
        params,
        headers: noAuthHeader(),
      }),
      providesTags: ['ApprovedSiteVisits'],
    }),
  }),
});

export const { useGetApprovedSiteVisitsQuery } = approvedSiteVisitsApiSlice;

const ApprovedSiteVisitsReport: React.FC<{ data: any }> = ({ data }) => {
  const [combinedResults, setCombinedResults] = useState<any[]>([]);

  // Extract date range parameters from data.params if available
  const dateRangeParams = data?.params || {};
  const { pickup_date__gte, pickup_date__lte, start_date, end_date } = dateRangeParams;

  // Use the correct date parameters (start_date/end_date for approved-site-visits API)
  const startDate = start_date || pickup_date__gte;
  const endDate = end_date || pickup_date__lte;

  // Use the approved site visits API to fetch site visits with vehicle data
  // Note: Date filtering is handled by ReportsModal, so we fetch all data
  const { data: approvedSiteVisitsData } = useGetApprovedSiteVisitsQuery({
    page: 1,
    page_size: 1000,
  });

  // Debug the date parameters being sent to API
  React.useEffect(() => {
    console.group('📅 DATE FILTERING DEBUG');
    console.log('Date range params from data.params:', dateRangeParams);
    console.log('pickup_date__gte:', pickup_date__gte);
    console.log('pickup_date__lte:', pickup_date__lte);
    console.log('start_date:', start_date);
    console.log('end_date:', end_date);
    console.log('Final startDate:', startDate);
    console.log('Final endDate:', endDate);
    console.log('API call parameters:', {
      start_date: startDate,
      end_date: endDate,
      page: 1,
      page_size: 1000,
    });
    console.groupEnd();
  }, [pickup_date__gte, pickup_date__lte, start_date, end_date, startDate, endDate, dateRangeParams]);

  // Get ALL special assignments first, then filter by status on client side
  // Date filtering is handled by ReportsModal, so we fetch all data
  const { data: specialAssignmentsData } = useGetSpecialBookingsQuery({
    page: 1,
    page_size: 1000,
  });

  // Debug log the raw special assignments data to check status
  React.useEffect(() => {
    if (specialAssignmentsData) {
      const assignments = specialAssignmentsData?.data?.results || [];
      console.log('🔍 Raw Special Assignments Status Check:', {
        totalCount: assignments.length,
        statusBreakdown: assignments.reduce((acc: any, assignment: any) => {
          const status = assignment.status || 'No Status';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {}),
        sampleAssignments: assignments.slice(0, 5).map((assignment: any) => ({
          id: assignment.id,
          status: assignment.status,
          reservation_date: assignment.reservation_date,
          destination: assignment.destination
        }))
      });
    }
  }, [specialAssignmentsData]);

  // Get vehicles data for special assignments vehicle lookup
  const { data: vehiclesData } = useGetVehiclesQuery({
    page: 1,
    page_size: 1000,
  });

  // Debug log approved site visits data
  React.useEffect(() => {
    if (approvedSiteVisitsData) {
      console.group('🏠 APPROVED SITE VISITS API DATA DEBUG');
      console.log('Raw approved site visits response:', approvedSiteVisitsData);
      console.log('Approved site visits array:', approvedSiteVisitsData?.results);
      console.log('Approved site visits count:', approvedSiteVisitsData?.results?.length || 0);

      if (approvedSiteVisitsData?.results?.length > 0) {
        console.log('Sample approved site visit structure:', approvedSiteVisitsData.results[0]);
        console.log('Vehicle data in approved site visits:', approvedSiteVisitsData.results.map((v: any) => ({
          site_visit_id: v.site_visit_id,
          vehicle_make: v.vehicle_make,
          vehicle_model: v.vehicle_model,
          vehicle_registration: v.vehicle_registration
        })));
      }
      console.groupEnd();
    }
  }, [approvedSiteVisitsData]);

  // Debug log vehicles data for special assignments
  React.useEffect(() => {
    if (vehiclesData) {
      console.group('🚗 VEHICLES DATA DEBUG (For Special Assignments)');
      console.log('Raw vehicles response:', vehiclesData);
      console.log('Vehicles array:', vehiclesData?.data?.results);
      console.log('Vehicles count:', vehiclesData?.data?.results?.length || 0);

      if (vehiclesData?.data?.results?.length > 0) {
        console.log('Sample vehicle structure:', vehiclesData.data.results[0]);
        console.log('All vehicle IDs:', vehiclesData.data.results.map((v: any) => ({ id: v.id, make: v.make, model: v.model, registration: v.vehicle_registration || v.registration })));
      }
      console.groupEnd();
    }
  }, [vehiclesData]);

  // Simplified helper function to get vehicle information
  const getVehicleInfo = (visit: any) => {
    const vehicles = vehiclesData?.data?.results || [];

    // Debug log for each visit's vehicle data
    console.group(`🚗 VEHICLE INFO DEBUG - Visit ID: ${visit.id || visit.site_visit_id} (Type: ${visit.is_special_assignment ? 'Special Assignment' : 'Site Visit'})`);
    console.log('Visit object:', visit);
    console.log('Available vehicles:', vehicles.length);
    console.log('All visit keys:', Object.keys(visit));

    // For approved site visits (from approved-site-visits API) - use direct fields
    if (!visit.is_special_assignment && (visit.vehicle_make || visit.vehicle_model || visit.vehicle_registration)) {
      console.log('✅ Using approved-site-visits API vehicle fields directly');
      const result = {
        make: visit.vehicle_make,
        model: visit.vehicle_model,
        registration: visit.vehicle_registration,
        id: null // No vehicle ID in this API response
      };
      console.log('Vehicle info result (approved-site-visits API):', result);
      console.groupEnd();
      return result;
    }

    // For special assignments, look up vehicle by ID
    if (visit.is_special_assignment && visit.vehicle && (typeof visit.vehicle === 'number' || typeof visit.vehicle === 'string')) {
      const vehicleId = typeof visit.vehicle === 'string' ? parseInt(visit.vehicle, 10) : visit.vehicle;
      console.log('🔍 Special Assignment - Looking for vehicle by ID:', vehicleId);
      const vehicleData = vehicles.find((v: any) => v.id === vehicleId);
      console.log('Found vehicle data for special assignment:', vehicleData);
      if (vehicleData) {
        const result = {
          make: vehicleData.make,
          model: vehicleData.model,
          registration: vehicleData.vehicle_registration || vehicleData.registration,
          id: vehicleData.id
        };
        console.log('✅ Special Assignment vehicle info result:', result);
        console.groupEnd();
        return result;
      } else {
        console.log('❌ No vehicle found for special assignment with ID:', vehicleId);
      }
    }

    // Fallback for any other cases
    console.log('⚠️ Using fallback - no vehicle data found');
    const result = {
      make: visit.vehicle_make || null,
      model: visit.vehicle_model || null,
      registration: visit.vehicle_registration || null
    };
    console.log('Vehicle info result (fallback):', result);
    console.groupEnd();
    return result;
  };

  // Combine site visits and special assignments with date range filtering
  useEffect(() => {
    // Get site visits from approved-site-visits API
    const siteVisits = approvedSiteVisitsData?.results || [];

    console.group('📋 APPROVED SITE VISITS API DATA PROCESSING');
    console.log('🔍 Raw approved site visits data:', approvedSiteVisitsData);
    console.log('📊 Extracted site visits array:', siteVisits);
    console.log('📊 Site visits count:', siteVisits.length);

    // Transform approved-site-visits API response to match expected structure
    const transformedSiteVisits = siteVisits.map((visit: any) => {
      console.log('🔄 Transforming approved-site-visits API response:', visit);
      return {
        ...visit,
        id: visit.site_visit_id, // Map site_visit_id to id
        marketer: visit.marketer_name, // Map marketer_name to marketer
        project: visit.project_name, // Map project_name to project
        driver: visit.driver_name, // Map driver_name to driver
        // Vehicle fields are already correctly named
        vehicle_make: visit.vehicle_make,
        vehicle_model: visit.vehicle_model,
        vehicle_registration: visit.vehicle_registration,
        // Add client count for display
        client_count: visit.client_count,
        // Mark as site visit (not special assignment)
        is_special_assignment: false,
        original_api: 'approved-site-visits'
      };
    });

    console.log('📊 Transformed site visits:', transformedSiteVisits);
    console.groupEnd();

    // Log complete structure of first few site visits
    if (transformedSiteVisits.length > 0) {
      console.group('🏠 COMPLETE SITE VISITS STRUCTURE (TRANSFORMED)');
      transformedSiteVisits.slice(0, 3).forEach((visit: any, index: number) => {
        console.group(`Site Visit ${index + 1} - ID: ${visit.id} (API: ${visit.original_api})`);
        console.log('Complete visit object:', visit);
        console.log('All keys in visit:', Object.keys(visit));
        console.log('Vehicle-related fields:', {
          vehicle: visit.vehicle,
          vehicle_id: visit.vehicle_id,
          vehicle_make: visit.vehicle_make,
          vehicle_model: visit.vehicle_model,
          vehicle_registration: visit.vehicle_registration,
          assigned_vehicle: visit.assigned_vehicle,
          assigned_vehicle_id: visit.assigned_vehicle_id
        });
        console.log('🔍 CRITICAL: Does vehicle_id exist?', {
          hasVehicleId: 'vehicle_id' in visit,
          vehicleIdValue: visit.vehicle_id,
          vehicleIdType: typeof visit.vehicle_id
        });
        console.log('🔍 ALL POSSIBLE VEHICLE FIELDS:', {
          allKeys: Object.keys(visit),
          vehicleRelatedKeys: Object.keys(visit).filter(key =>
            key.toLowerCase().includes('vehicle') ||
            key.toLowerCase().includes('car') ||
            key.toLowerCase().includes('transport')
          ),
          vehicleRelatedValues: Object.keys(visit)
            .filter(key =>
              key.toLowerCase().includes('vehicle') ||
              key.toLowerCase().includes('car') ||
              key.toLowerCase().includes('transport')
            )
            .reduce((acc: any, key: string) => {
              acc[key] = visit[key];
              return acc;
            }, {})
        });
        console.log('Driver-related fields:', {
          driver: visit.driver,
          driver_id: visit.driver_id,
          driver_name: visit.driver_name,
          assigned_driver: visit.assigned_driver,
          assigned_driver_id: visit.assigned_driver_id
        });
        console.log('Other important fields:', {
          status: visit.status,
          pickup_date: visit.pickup_date,
          pickup_time: visit.pickup_time,
          pickup_location: visit.pickup_location,
          project: visit.project,
          project_name: visit.project_name,
          marketer: visit.marketer,
          marketer_name: visit.marketer_name
        });
        console.groupEnd();
      });
      console.groupEnd();
    }
    console.groupEnd();
    
    // Get special assignments from query
    const specialAssignments = specialAssignmentsData?.data?.results || [];

    // Debug log the complete special assignments payload
    console.group('📋 COMPLETE SPECIAL ASSIGNMENTS PAYLOAD DEBUG');
    console.log('🔍 Raw specialAssignmentsData (complete):', specialAssignmentsData);
    console.log('🔍 Special assignments structure analysis:', {
      hasData: !!specialAssignmentsData?.data,
      hasResults: !!specialAssignmentsData?.data?.results,
      resultsLength: specialAssignments.length,
      dataKeys: specialAssignmentsData ? Object.keys(specialAssignmentsData) : [],
      dataDataKeys: specialAssignmentsData?.data ? Object.keys(specialAssignmentsData.data) : []
    });

    console.log('📊 Extracted special assignments array:', specialAssignments);
    console.log('📊 Special assignments count:', specialAssignments.length);

    // Log complete structure of first few special assignments
    if (specialAssignments.length > 0) {
      console.group('📋 COMPLETE SPECIAL ASSIGNMENTS STRUCTURE');
      specialAssignments.slice(0, 3).forEach((assignment: any, index: number) => {
        console.group(`Special Assignment ${index + 1} - ID: ${assignment.id}`);
        console.log('Complete assignment object:', assignment);
        console.log('All keys in assignment:', Object.keys(assignment));
        console.log('Vehicle-related fields:', {
          vehicle: assignment.vehicle,
          vehicle_id: assignment.vehicle_id,
          vehicle_make: assignment.vehicle_make,
          vehicle_model: assignment.vehicle_model,
          vehicle_registration: assignment.vehicle_registration,
          assigned_vehicle: assignment.assigned_vehicle,
          assigned_vehicle_id: assignment.assigned_vehicle_id
        });
        console.log('Driver-related fields:', {
          driver: assignment.driver,
          driver_id: assignment.driver_id,
          driver_name: assignment.driver_name,
          assigned_driver: assignment.assigned_driver,
          assigned_driver_id: assignment.assigned_driver_id
        });
        console.log('Other important fields:', {
          status: assignment.status,
          reservation_date: assignment.reservation_date,
          reservation_time: assignment.reservation_time,
          pickup_location: assignment.pickup_location,
          destination: assignment.destination,
          assigned_to: assignment.assigned_to
        });
        console.groupEnd();
      });
      console.groupEnd();
    }
    console.groupEnd();

    // Enhanced logging for debugging date filtering issues
    console.group('🔍 ApprovedSiteVisitsReports - Data Processing');
    console.log('📅 Date range params:', dateRangeParams);
    console.log('🏠 Site visits count:', siteVisits.length);
    console.log('📋 Special assignments count:', specialAssignments.length);

    // Debug vehicle-related fields in site visits
    if (siteVisits.length > 0) {
      console.group('🚗 SITE VISITS VEHICLE FIELDS DEBUG');
      console.log('Sample site visit (full object):', siteVisits[0]);
      siteVisits.slice(0, 3).forEach((visit: any, index: number) => {
        console.log(`Site Visit ${index + 1} vehicle fields:`, {
          id: visit.id,
          vehicle: visit.vehicle,
          vehicle_id: visit.vehicle_id,
          vehicle_make: visit.vehicle_make,
          vehicle_model: visit.vehicle_model,
          vehicle_registration: visit.vehicle_registration,
          driver: visit.driver,
          driver_id: visit.driver_id,
          driver_name: visit.driver_name
        });
      });
      console.groupEnd();
    }

    // Debug vehicle-related fields in special assignments
    if (specialAssignments.length > 0) {
      console.group('🚗 SPECIAL ASSIGNMENTS VEHICLE FIELDS DEBUG');
      console.log('Sample special assignment (full object):', specialAssignments[0]);
      specialAssignments.slice(0, 3).forEach((assignment: any, index: number) => {
        console.log(`Special Assignment ${index + 1} vehicle fields:`, {
          id: assignment.id,
          vehicle: assignment.vehicle,
          vehicle_id: assignment.vehicle_id,
          vehicle_make: assignment.vehicle_make,
          vehicle_model: assignment.vehicle_model,
          vehicle_registration: assignment.vehicle_registration,
          driver: assignment.driver,
          driver_id: assignment.driver_id,
          driver_name: assignment.driver_name
        });
      });
      console.groupEnd();
    }

    console.log('📋 Raw special assignments sample:', specialAssignments.slice(0, 3).map((assignment: any) => ({
      id: assignment.id,
      reservation_date: assignment.reservation_date,
      destination: assignment.destination,
      status: assignment.status
    })));
    console.groupEnd();
    
    // Transform special assignments to match site visit structure - ONLY APPROVED ONES
    const transformedAssignments = specialAssignments
      .filter((assignment: any) => {
        // Strict status filtering - only include Approved assignments
        const status = assignment.status?.toLowerCase?.() || assignment.status || '';
        const isApproved = status === 'approved' || status === 'Approved';

        console.log(`📋 Special Assignment Status Filter:`, {
          id: assignment.id,
          originalStatus: assignment.status,
          normalizedStatus: status,
          isApproved: isApproved,
          action: isApproved ? '✅ INCLUDED' : '❌ FILTERED OUT (Not Approved)'
        });

        return isApproved;
      })
      .map((assignment: any) => ({
        ...assignment,
        pickup_date: assignment.reservation_date,
        pickup_time: assignment.reservation_time,
        pickup_location: assignment.pickup_location,
        project: assignment.destination,
        marketer: typeof assignment.assigned_to === 'object'
          ? (assignment.assigned_to?.dp_name || assignment.assigned_to?.name || 'Unknown Department')
          : assignment.assigned_to || 'Unknown',
        is_special_assignment: true,
      }));

    // No date filtering needed here - ReportsModal handles all date filtering
    const finalSiteVisits = transformedSiteVisits;

    // Only filter special assignments by status (approved only)
    const filteredAssignments = transformedAssignments.filter((assignment: any) => {
      const status = assignment.status?.toLowerCase?.() || assignment.status || '';
      const isApproved = status === 'approved' || status === 'Approved';

      console.log(`📋 Special Assignment Status Filter:`, {
        id: assignment.id,
        originalStatus: assignment.status,
        normalizedStatus: status,
        isApproved: isApproved,
        action: isApproved ? '✅ INCLUDED' : '❌ FILTERED OUT (Not Approved)'
      });

      return isApproved;
    });

    console.log('📊 ApprovedSiteVisits Final Results:', {
      originalSiteVisits: finalSiteVisits.length,
      originalSpecialAssignments: transformedAssignments.length,
      filteredSpecialAssignments: filteredAssignments.length,
      statusBreakdown: {
        siteVisitStatuses: [...new Set(finalSiteVisits.map((item: any) => item.status))],
        specialAssignmentStatuses: [...new Set(filteredAssignments.map((item: any) => item.status))]
      }
    });

    // Combine results (site visits + approved special assignments)
    setCombinedResults([...finalSiteVisits, ...filteredAssignments]);
  }, [approvedSiteVisitsData, specialAssignmentsData, vehiclesData]);

  if (!combinedResults?.length) {
    return (
      <Card className="w-full">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <CheckCircle2 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground">No Approved Visits</h3>
          <p className="text-sm text-muted-foreground mt-2">
            No approved site visits or special assignments found for the selected date range.
          </p>
          {(startDate || endDate) && (
            <div className="mt-4 text-xs text-muted-foreground bg-muted/30 p-3 rounded-md">
              <p className="font-medium">Date Range Filter Applied:</p>
              <p>From: {startDate || 'Not specified'}</p>
              <p>To: {endDate || 'Not specified'}</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusLower = status?.toLowerCase();
    switch (statusLower) {
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200"><CheckCircle2 className="w-3 h-3 mr-1" />{status}</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200"><Clock className="w-3 h-3 mr-1" />{status}</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />{status}</Badge>;
      default:
        return <Badge variant="outline">{status || 'Unknown'}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '-';
    try {
      const time = timeString.includes('T') ? new Date(timeString) : new Date(`2000-01-01T${timeString}`);
      return time.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return timeString;
    }
  };

  const getClientCount = (item: any) => {
    // For approved-site-visits API response
    if (item.client_count && typeof item.client_count === 'number') {
      return item.client_count;
    }
    // For standard site visits API response
    if (item.site_visit_client && Array.isArray(item.site_visit_client)) {
      return item.site_visit_client.length;
    }
    if (item.clients && Array.isArray(item.clients)) {
      return item.clients.length;
    }
    // If clients is a string (like "Repher Mukonzo (+254719463209)"), extract count
    if (typeof item.clients === 'string') {
      // Count the number of phone numbers in the string (assuming each client has a phone number in parentheses)
      const phoneMatches = item.clients.match(/\(\+\d+\)/g);
      return phoneMatches ? phoneMatches.length : 1; // Default to 1 if no phone pattern found
    }
    return 0;
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <CheckCircle2 className="h-5 w-5 text-green-600" />
          Approved Visits Report
        </CardTitle>
        <CardDescription>
          Overview of all approved site visits and special assignments with detailed information
        </CardDescription>
        {(startDate || endDate) && (
          <div className="mt-2 text-xs text-muted-foreground bg-muted/30 p-2 rounded-md inline-flex items-center">
            <CalendarDays className="h-3 w-3 mr-1" />
            Filtered: {startDate ? formatDate(startDate) : 'Any'} to {endDate ? formatDate(endDate) : 'Any'}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-4 w-4" />
                    Date & Time
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Briefcase className="h-4 w-4" />
                    Type
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Marketer
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    No. of Clients
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Project/Destination
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Driver
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Car className="h-4 w-4" />
                    Vehicle
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Pickup Location
                  </div>
                </TableHead>
                <TableHead className="font-semibold">Status</TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    Remarks
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {combinedResults.map((visit: any, i: number) => {
                // Create unique key to avoid conflicts between site visits and special assignments
                const uniqueKey = visit.is_special_assignment
                  ? `special-assignment-${visit.id || i}-${visit.reservation_date || ''}-${i}`
                  : `site-visit-${visit.id || visit.site_visit_id || i}-${visit.pickup_date || ''}-${i}`;

                return (
                <TableRow key={uniqueKey} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{formatDate(visit.pickup_date)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatTime(visit.pickup_time)}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {visit.is_special_assignment ? (
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                          <Briefcase className="w-3 h-3 mr-1" />
                          Special Assignment
                        </Badge>
                      ) : (
                        <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">
                          <Building2 className="w-3 h-3 mr-1" />
                          Site Visit
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">
                        {typeof visit.marketer === 'object'
                          ? (visit.marketer?.fullnames || visit.marketer?.name || visit.marketer?.dp_name || 'Unknown')
                          : (visit.marketer || visit.marketer_name || '-')
                        }
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{getClientCount(visit)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span>{visit.project || visit.project_name || visit.special_assignment_destination || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">
                        {typeof visit.driver === 'object'
                          ? (visit.driver?.fullnames || visit.driver?.name || visit.driver?.first_name + ' ' + visit.driver?.last_name || 'Unknown Driver')
                          : (visit.driver || visit.driver_name || '-')
                        }
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm">
                        {(() => {
                          const vehicleInfo = getVehicleInfo(visit);
                          return (
                            <>
                              <div className="font-medium">
                                {[vehicleInfo.make, vehicleInfo.model]
                                  .filter(Boolean)
                                  .join(" ") || visit.vehicle || '-'}
                              </div>
                              {vehicleInfo.registration && (
                                <div className="text-xs font-semibold text-primary bg-primary/10 px-2 py-1 rounded mt-1">
                                  {vehicleInfo.registration}
                                </div>
                              )}
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{visit.pickup_location || visit.location || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(visit.status)}
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      {visit.remarks ? (
                        <div className="flex items-start gap-2">
                          <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{visit.remarks}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
        <div className="mt-4 text-sm text-muted-foreground">
          Showing {combinedResults.length} approved visit{combinedResults.length !== 1 ? 's' : ''} (site visits & special assignments)
          {(startDate || endDate) && (
            <span> • Date filtered: {startDate ? formatDate(startDate) : 'Any'} to {endDate ? formatDate(endDate) : 'Any'}</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
export default ApprovedSiteVisitsReport;
